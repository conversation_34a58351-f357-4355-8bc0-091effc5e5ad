2025-08-08 13:48:50 - MainProcess - 6948 - mobile - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\ChinaMobile\logs\20250808
2025-08-08 13:48:52 - SpawnProcess-1 - 33368 - mobile - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\ChinaMobile\logs\20250808
2025-08-08 13:48:57 - SpawnProcess-1 - 33368 - mobile - DEBUG - [mobile_crawler.py:75] - 确保目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808
2025-08-08 13:48:57 - SpawnProcess-1 - 33368 - mobile - DEBUG - [mobile_crawler.py:75] - 确保目录存在: D:\PythonProjects\xty\ChinaMobile\zip\20250808
2025-08-08 13:48:57 - SpawnProcess-1 - 33368 - mobile - DEBUG - [mobile_crawler.py:75] - 确保目录存在: D:\PythonProjects\xty\ChinaMobile\logs\20250808
2025-08-08 13:48:57 - SpawnProcess-1 - 33368 - mobile - INFO - [mobile_crawler.py:1271] - 正在加载省份信息...
2025-08-08 13:48:57 - SpawnProcess-1 - 33368 - mobile - INFO - [mobile_crawler.py:1295] - 选中的省份：北京(ID: 100)
2025-08-08 13:48:57 - SpawnProcess-1 - 33368 - mobile - INFO - [mobile_crawler.py:1298] - 正在初始化浏览器...
2025-08-08 13:50:37 - MainProcess - 3900 - mobile - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\ChinaMobile\logs\20250808
2025-08-08 13:50:38 - SpawnProcess-1 - 7932 - mobile - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\ChinaMobile\logs\20250808
2025-08-08 13:50:41 - SpawnProcess-1 - 7932 - mobile - DEBUG - [mobile_crawler.py:75] - 确保目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808
2025-08-08 13:50:41 - SpawnProcess-1 - 7932 - mobile - DEBUG - [mobile_crawler.py:75] - 确保目录存在: D:\PythonProjects\xty\ChinaMobile\zip\20250808
2025-08-08 13:50:41 - SpawnProcess-1 - 7932 - mobile - DEBUG - [mobile_crawler.py:75] - 确保目录存在: D:\PythonProjects\xty\ChinaMobile\logs\20250808
2025-08-08 13:50:41 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:1271] - 正在加载省份信息...
2025-08-08 13:50:41 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:1295] - 选中的省份：广东(ID: 200)
2025-08-08 13:50:41 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:1298] - 正在初始化浏览器...
2025-08-08 13:50:43 - SpawnProcess-1 - 7932 - mobile - DEBUG - [mobile_crawler.py:311] - Chrome WebDriver初始化成功
2025-08-08 13:50:43 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:1311] - 正在爬取 广东
2025-08-08 13:50:43 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:974] - 开始爬取广东(ID: 200)的资费信息
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:996] - 浏览器页面加载完成
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:1005] - Anaconda Prompt窗口已调整到右半屏并置顶
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:1009] - 录屏已开始，文件: D:\PythonProjects\xty\ChinaMobile\zip\20250808\广东_mobile_114735_资费数据.mp4
2025-08-08 13:50:47 - SpawnProcess-1 - 7932 - mobile - DEBUG - [mobile_crawler.py:1021] - 已访问页面: https://h.app.coc.10086.cn/cmcc-app/pc-pages/tariffZonePers.html?prov=200
2025-08-08 13:50:50 - SpawnProcess-1 - 7932 - mobile - DEBUG - [mobile_crawler.py:1025] - 页面加载完成
2025-08-08 13:50:52 - SpawnProcess-1 - 7932 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 13:50:52 - SpawnProcess-1 - 7932 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_135052_4886_初始页面.html
2025-08-08 13:50:52 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:1085] - 广东没有资费大类，直接处理资费来源和类型
2025-08-08 13:50:53 - SpawnProcess-1 - 7932 - mobile - DEBUG - [mobile_crawler.py:1174] - 已选择资费来源: 全网资费
2025-08-08 13:50:55 - SpawnProcess-1 - 7932 - mobile - DEBUG - [mobile_crawler.py:1195] - 已选择资费类型: 套餐
2025-08-08 13:51:46 - SpawnProcess-1 - 7932 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 13:51:46 - SpawnProcess-1 - 7932 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_135146_6781_滚动加载完成_全网资费_套餐.html
2025-08-08 13:51:46 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:578] - 找到 21 个资费项目，开始批量处理...
2025-08-08 13:52:16 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:599] - 处理完成，共获得 52 条资费数据
2025-08-08 13:52:16 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:1205] - 成功获取 52 条全部-全网资费-套餐的资费数据
2025-08-08 13:52:16 - SpawnProcess-1 - 7932 - mobile - DEBUG - [mobile_crawler.py:1195] - 已选择资费类型: 加装包（不含港澳台和国际）
2025-08-08 13:55:12 - SpawnProcess-1 - 7932 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 13:55:12 - SpawnProcess-1 - 7932 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_135512_3555_滚动加载完成_全网资费_加装包（不含港澳台和国际）.html
2025-08-08 13:55:12 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:578] - 找到 262 个资费项目，开始批量处理...
2025-08-08 13:56:40 - SpawnProcess-1 - 7932 - mobile - INFO - [mobile_crawler.py:593] - 已处理 100/262 个资费项目，获得 103 条数据
