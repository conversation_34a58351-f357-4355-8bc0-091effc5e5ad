2025-08-08 14:22:50 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1213] - 浏览器页面加载完成
2025-08-08 14:22:50 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1222] - Anaconda Prompt窗口已调整到右半屏并置顶
2025-08-08 14:22:50 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1226] - 录屏已开始，文件: D:\PythonProjects\xty\ChinaMobile\zip\20250808\广东_mobile_404885_资费数据.mp4
2025-08-08 14:22:51 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1238] - 已访问页面: https://h.app.coc.10086.cn/cmcc-app/pc-pages/tariffZonePers.html?prov=200
2025-08-08 14:22:55 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1242] - 页面加载完成
2025-08-08 14:22:57 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 14:22:57 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_142257_3737_初始页面.html
2025-08-08 14:22:57 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1302] - 广东没有资费大类，直接处理资费来源和类型
2025-08-08 14:22:57 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1391] - 已选择资费来源: 全网资费
2025-08-08 14:22:59 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1412] - 已选择资费类型: 套餐
2025-08-08 14:23:51 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 14:23:51 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_142351_4975_滚动加载完成_全网资费_套餐.html
2025-08-08 14:23:51 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:574] - 开始使用JavaScript批量提取资费数据...
2025-08-08 14:23:51 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:676] - JavaScript提取完成，耗时 0.02 秒，获得 52 条数据
2025-08-08 14:23:51 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1422] - 成功获取 52 条全部-全网资费-套餐的资费数据
2025-08-08 14:23:51 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1412] - 已选择资费类型: 加装包（不含港澳台和国际）
2025-08-08 14:26:47 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 14:26:47 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_142647_3273_滚动加载完成_全网资费_加装包（不含港澳台和国际）.html
2025-08-08 14:26:47 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:574] - 开始使用JavaScript批量提取资费数据...
2025-08-08 14:26:47 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:676] - JavaScript提取完成，耗时 0.31 秒，获得 1260 条数据
2025-08-08 14:26:47 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1422] - 成功获取 1260 条全部-全网资费-加装包（不含港澳台和国际）的资费数据
2025-08-08 14:26:47 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1412] - 已选择资费类型: 营销活动
2025-08-08 14:27:28 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 14:27:28 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_142728_3056_滚动加载完成_全网资费_营销活动.html
2025-08-08 14:27:28 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:574] - 开始使用JavaScript批量提取资费数据...
2025-08-08 14:27:28 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:676] - JavaScript提取完成，耗时 0.01 秒，获得 21 条数据
2025-08-08 14:27:28 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1422] - 成功获取 21 条全部-全网资费-营销活动的资费数据
2025-08-08 14:27:28 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1412] - 已选择资费类型: 国际/港澳台资费
2025-08-08 14:28:21 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 14:28:21 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_142821_4215_滚动加载完成_全网资费_国际_港澳台资费.html
2025-08-08 14:28:21 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:574] - 开始使用JavaScript批量提取资费数据...
2025-08-08 14:28:21 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:676] - JavaScript提取完成，耗时 0.20 秒，获得 678 条数据
2025-08-08 14:28:21 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1422] - 成功获取 678 条全部-全网资费-国际/港澳台资费的资费数据
2025-08-08 14:28:21 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1391] - 已选择资费来源: 广东资费
2025-08-08 14:28:23 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1412] - 已选择资费类型: 套餐
2025-08-08 14:29:53 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 14:29:53 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_142953_3778_滚动加载完成_广东资费_套餐.html
2025-08-08 14:29:53 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:574] - 开始使用JavaScript批量提取资费数据...
2025-08-08 14:29:54 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:676] - JavaScript提取完成，耗时 0.48 秒，获得 1420 条数据
2025-08-08 14:29:54 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1422] - 成功获取 1420 条全部-广东资费-套餐的资费数据
2025-08-08 14:29:54 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1412] - 已选择资费类型: 加装包（不含港澳台和国际）
2025-08-08 14:31:31 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 14:31:31 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_143131_4373_滚动加载完成_广东资费_加装包（不含港澳台和国际）.html
2025-08-08 14:31:31 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:574] - 开始使用JavaScript批量提取资费数据...
2025-08-08 14:31:31 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:676] - JavaScript提取完成，耗时 0.19 秒，获得 1141 条数据
2025-08-08 14:31:31 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1422] - 成功获取 1141 条全部-广东资费-加装包（不含港澳台和国际）的资费数据
2025-08-08 14:31:32 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1412] - 已选择资费类型: 营销活动
2025-08-08 14:32:32 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 14:32:32 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_143232_1978_滚动加载完成_广东资费_营销活动.html
2025-08-08 14:32:32 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:574] - 开始使用JavaScript批量提取资费数据...
2025-08-08 14:32:33 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:676] - JavaScript提取完成，耗时 0.43 秒，获得 1328 条数据
2025-08-08 14:32:33 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1422] - 成功获取 1328 条全部-广东资费-营销活动的资费数据
2025-08-08 14:32:33 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1412] - 已选择资费类型: 国际/港澳台资费
2025-08-08 14:33:13 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 14:33:13 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_143313_8839_滚动加载完成_广东资费_国际_港澳台资费.html
2025-08-08 14:33:13 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:574] - 开始使用JavaScript批量提取资费数据...
2025-08-08 14:33:13 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:676] - JavaScript提取完成，耗时 0.02 秒，获得 68 条数据
2025-08-08 14:33:13 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1422] - 成功获取 68 条全部-广东资费-国际/港澳台资费的资费数据
2025-08-08 14:33:14 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1412] - 已选择资费类型: 国内标准资费
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:78] - 确保省份数据目录存在: D:\PythonProjects\xty\ChinaMobile\data\20250808\200
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - INFO - [html_saver.py:199] - Selenium HTML已保存到: D:\PythonProjects\xty\ChinaMobile\data\20250808\200\广东_20250808_143354_2479_滚动加载完成_广东资费_国内标准资费.html
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:574] - 开始使用JavaScript批量提取资费数据...
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:676] - JavaScript提取完成，耗时 0.01 秒，获得 31 条数据
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1422] - 成功获取 31 条全部-广东资费-国内标准资费的资费数据
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1306] - 成功获取 5999 条资费数据
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - ERROR - [mobile_crawler.py:223] - 保存Excel文件时发生错误: 'NoneType' object has no attribute 'replace'
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - ERROR - [mobile_crawler.py:223] - 保存Excel文件时发生错误: 'NoneType' object has no attribute 'replace'
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - ERROR - [mobile_crawler.py:223] - 保存Excel文件时发生错误: 'NoneType' object has no attribute 'replace'
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - ERROR - [mobile_crawler.py:223] - 保存Excel文件时发生错误: 'NoneType' object has no attribute 'replace'
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - ERROR - [mobile_crawler.py:223] - 保存Excel文件时发生错误: 'NoneType' object has no attribute 'replace'
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:172] - 已添加文件到ZIP: .
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:172] - 已添加文件到ZIP: .
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:172] - 已添加文件到ZIP: .
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:172] - 已添加文件到ZIP: .
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:172] - 已添加文件到ZIP: .
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:176] - 已打包为zip: D:\PythonProjects\xty\ChinaMobile\zip\20250808\广东_mobile_404885_资费数据.zip
2025-08-08 14:33:54 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1334] - 成功爬取广东的资费信息，共5999条数据
2025-08-08 14:33:55 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1345] - 录屏已结束，文件: D:\PythonProjects\xty\ChinaMobile\zip\20250808\广东_mobile_404885_资费数据.mp4
2025-08-08 14:33:58 - SpawnProcess-1 - 3824 - mobile - DEBUG - [mobile_crawler.py:1158] - 回调结果: {'status': 'error', 'message': "Cannot connect to host ************:8082 ssl:default [Connect call failed ('************', 8082)]"}
2025-08-08 14:34:00 - SpawnProcess-1 - 3824 - mobile - INFO - [mobile_crawler.py:1466] - 浏览器已关闭
