# 中国电信资费信息爬虫业务说明文档

## 目录
- [1. 系统概述](#1-系统概述)
- [2. 业务流程](#2-业务流程)
- [3. 数据采集逻辑](#3-数据采集逻辑)
- [4. 省份定制化解析器详解](#4-省份定制化解析器详解)
- [5. 页面操作流程](#5-页面操作流程)
- [6. 数据结构说明](#6-数据结构说明)
- [7. 时间控制策略](#7-时间控制策略)
- [8. 质量保证机制](#8-质量保证机制)
- [9. 输出结果说明](#9-输出结果说明)

---

## 1. 系统概述

### 1.1 功能介绍
中国电信资费信息爬虫是一个高度定制化的自动化数据采集系统，能够：
- 自动访问中国电信各省份的官方资费页面
- 支持31个省份的个性化数据采集策略
- 智能处理iframe嵌套页面和API接口数据
- 自动适配不同省份的页面结构和交互方式
- 生成标准化的JSON数据文件和ZIP压缩包

### 1.2 系统架构
```
用户请求 → API接口 → 爬虫引擎 → 省份路由器 → 定制化解析器 → 数据处理 → JSON文件 → ZIP打包
                                    ↓
                            [15个定制化省份解析器]
                                    ↓
                            [16个API接口省份]
```

### 1.3 核心价值
- **全省份覆盖**：支持全国31个省份的电信资费信息采集
- **高度定制化**：针对15个省份开发了专门的解析器
- **多种数据源**：支持HTML页面解析和API接口两种数据获取方式
- **智能适配**：自动识别页面结构变化，采用最适合的采集策略

### 1.4 技术特色
- **省份解析器映射**：每个省份都有对应的解析函数和菜单配置
- **iframe页面处理**：专门处理电信页面中的iframe嵌套结构
- **API拦截技术**：拦截并保存API响应数据
- **城市遍历模式**：江苏省支持按城市分别采集数据

---

## 2. 业务流程

### 2.1 整体业务流程图
```
开始任务
    ↓
确定目标省份
    ↓
访问省份资费页面
    ↓
检查省份类型
    ↓
┌─────────────────┬─────────────────┐
│  定制化省份      │   API接口省份    │
│  (15个省份)     │   (16个省份)    │
└─────────────────┴─────────────────┘
    ↓                      ↓
进入iframe页面          设置API拦截器
    ↓                      ↓
使用定制化解析器        触发API请求
    ↓                      ↓
解析页面内容           保存API响应
    ↓                      ↓
生成JSON文件           生成JSON文件
    ↓                      ↓
创建ZIP压缩包          创建ZIP压缩包
```

### 2.2 省份分类处理策略

**定制化解析省份（15个）**
- 黑龙江(hl)、宁夏(nx)、河北(he)、安徽(ah)、广东(gd)
- 江西(jx)、新疆(xj)、云南(yn)、江苏(js)、山东(sd)
- 甘肃(gs)、海南(hi)、湖南(hn)、西藏(xz)、浙江(zj)

**API接口省份（16个）**
- 北京(bj)、重庆(cq)、福建(fj)、广西(gx)、贵州(gz)
- 湖北(hb)、河南(ha)、吉林(jl)、辽宁(ln)、内蒙古(nm)
- 青海(qh)、山西(sx)、陕西(sn)、四川(sc)、天津(tj)、云南(yn)

---

## 3. 数据采集逻辑

### 3.1 省份路由机制
系统根据省份代码自动选择采集策略：

```python
# 省份解析器映射表
PROVINCE_PARSERS = {
    'hl': (heilongjiang.parse_heilongjiang_content, heilongjiang.get_heilongjiang_menu_config),
    'nx': (ningxia.parse_ningxia_content, ningxia.get_ningxia_menu_config),
    # ... 其他13个定制化省份
}

# API URL配置表
API_URLS = {
    'bj': 'https://bj.189.cn/expensesZoneDetail/queryExpensesZoneList.action',
    'cq': 'https://cq.189.cn/scloud/ecscactivity/psotage/queryNewPostAge',
    # ... 其他14个API省份
}
```

### 3.2 采集策略分类

**策略一：定制化HTML解析**
- 适用于：15个定制化省份
- 特点：每个省份有专门的解析器
- 流程：访问iframe页面 → 使用定制解析器 → 提取结构化数据

**策略二：API接口拦截**
- 适用于：16个API接口省份
- 特点：拦截网络请求获取JSON数据
- 流程：设置请求拦截器 → 触发API调用 → 保存响应数据

**策略三：江苏省城市遍历**
- 适用于：江苏省特殊处理
- 特点：在iframe页面内遍历所有城市
- 流程：进入iframe → 获取城市列表 → 逐城市采集数据

---

## 4. 省份定制化解析器详解

### 4.1 解析器架构设计

每个定制化省份都包含两个核心函数：
- `parse_[省份]_content()`: 页面内容解析函数
- `get_[省份]_menu_config()`: 菜单配置获取函数

### 4.2 黑龙江省解析器 (heilongjiang.py)

**页面特征**
- 产品容器：`div#ProductItem`
- 列表容器：`div#list`
- 页面结构相对简单，产品信息集中展示

**解析逻辑**
```python
# 等待列表区域加载
await page.wait_for_selector("div#list", timeout=30000)

# 获取所有产品项
products = await page.query_selector_all("div#ProductItem")

# 解析每个产品的详细信息
for product in products:
    # 提取标题、编号、基本信息、服务内容、备注等
```

**菜单配置**
- 等待选择器：`div.tab-top`
- 菜单选择器：`div.tab-top > ul > div.top > li`
- 列表选择器：`div#list`

### 4.3 宁夏省解析器 (ningxia.py)

**页面特征**
- 采用标签页结构
- 产品信息包含详细的基本信息和服务内容
- 备注信息可能包含多个段落

**解析逻辑**
```python
# 获取产品基本信息
basic_info_elems = await product.query_selector_all("div.text.jbxx div.clearfix div p")
for elem in basic_info_elems:
    text = await elem.text_content()
    if "：" in text:
        key, value = text.split("：", 1)
        basic_info[key.strip()] = value.strip()

# 获取备注信息（支持多段落）
remark_paragraphs = await remark_div.query_selector_all("p")
remark_texts = []
for p in remark_paragraphs:
    text = await p.text_content()
    if text.strip():
        remark_texts.append(text.strip())
remark = "\n".join(remark_texts)
```

**菜单配置**
- 等待选择器：`div.tab-top`
- 菜单选择器：`div.tab-top > ul > div.top > li`
- 列表选择器：`div.index-box#top`

### 4.4 河北省解析器 (hebei.py)

**页面特征**
- 页面结构与宁夏类似
- 产品信息展示方式标准化
- 支持多种资费类型

**解析特点**
- 标准化的产品信息提取
- 统一的数据结构处理
- 完善的错误处理机制

### 4.5 安徽省解析器 (anhui.py)

**页面特征**
- 产品列表结构清晰
- 基本信息和服务内容分离展示
- 备注信息格式规范

**解析逻辑**
- 采用标准的产品信息提取模式
- 支持复杂的服务内容解析
- 完整的数据验证机制

### 4.6 广东省解析器 (guangdong.py)

**页面特征**
- 采用现代化的页面布局
- 使用Tailwind CSS样式框架
- 产品信息展示更加丰富

**菜单配置**
- 等待选择器：`div.w-full.p-4.header-content`
- 菜单选择器：`div.w-full.flex.items-center > div.px-2.mt-1.mb-4.text-base.text-gray-400.cursor-pointer.relative`
- 列表选择器：`div.w-full.flex.flex-col.items-center.justify-center`

### 4.7 江西省解析器 (jiangxi.py)

**页面特征**
- 标签页导航结构
- 产品信息包含详细的备注说明
- 支持多段落备注信息

**解析特点**
- 完善的备注信息处理
- 支持多行文本内容
- 标准化的数据输出格式

**菜单配置**
- 等待选择器：`div.tab-top`
- 菜单选择器：`div.tab-top > ul > li`
- 列表选择器：`div.index-box#index-box`

### 4.8 新疆省解析器 (xinjiang.py)

**页面特征**
- 产品容器：`div.m-t-20.w100`
- 列表容器：`div.p-b-10.w100`
- 需要最大化浏览器窗口以确保内容完全显示

**解析逻辑**
```python
# 先最大化浏览器窗口
await page.evaluate("""() => {
    window.moveTo(0, 0);
    window.resizeTo(screen.availWidth, screen.availHeight);
}""")

# 获取所有产品项
products = await page.query_selector_all("div.m-t-20.w100")
```

### 4.9 云南省解析器 (yunnan.py)

**页面特征**
- 支持分页加载
- 需要处理"下一页"按钮
- 每页都需要保存HTML结构

**解析逻辑**
```python
# 分页处理逻辑
while has_next_page:
    # 保存当前页的HTML结构
    await save_page_html(page, menu_type, current_page)
    
    # 获取当前页的产品项
    products = await page.query_selector_all("div.list-box > div")
    
    # 检查是否有下一页
    next_button = await page.query_selector("a.next")
    if next_button and not await next_button.get_attribute("class").includes("disabled"):
        await next_button.click()
        current_page += 1
    else:
        has_next_page = False
```

**菜单配置**
- 等待选择器：`div.menu-box`
- 菜单选择器：`div.top.clearfix > div`
- 列表选择器：`div.tc_content`

### 4.10 江苏省解析器 (jiangsu.py) - 特殊城市遍历模式

**页面特征**
- 需要在iframe页面内遍历所有城市
- 每个城市都有独立的资费信息
- 城市选择器：`div.city`

**解析逻辑**
```python
async def handle_all_cities_js(self, new_page):
    # 获取所有城市名列表
    await new_page.wait_for_selector('div.city', timeout=10000)
    city_elements = await new_page.query_selector_all('div.city')
    
    for city_element in city_elements:
        city_name = await city_element.text_content()
        self.current_city = city_name
        
        # 点击城市选择
        await city_element.click()
        await self._random_sleep(2, 3)
        
        # 处理该城市的菜单项
        menu_items = await new_page.query_selector_all(menu_config['menu_selector'])
        for item in menu_items:
            # 解析城市资费数据
            await parse_content(new_page, item_text, self.all_products_data)
        
        # 保存城市数据
        await self._save_current_data(city_name)
```

**菜单配置**
- 等待选择器：`div.nav_main`
- 菜单选择器：`div.tab > span.lay`
- 列表选择器：`div.pro_list.showList`

### 4.11 山东省解析器 (shandong.py)

**页面特征**
- 标准的列表框结构
- 产品信息展示完整
- 数据结构规范

**菜单配置**
- 等待选择器：`div.menu-box`
- 菜单选择器：`div.top.clearfix > div`
- 列表选择器：`div.list-box`

### 4.12 甘肃省解析器 (gansu.py)

**页面特征**
- 列表框容器：`div.list-box`
- 产品信息结构化展示
- 支持完整的产品信息提取

### 4.13 海南省解析器 (hainan.py)

**页面特征**
- 与甘肃省类似的页面结构
- 标准化的产品信息展示
- 完善的数据提取逻辑

### 4.14 湖南省解析器 (hunan.py)

**页面特征**
- 采用van-list组件
- 支持滚动加载
- 需要PageDown键滚动加载内容

**解析逻辑**
```python
# 使用PageDown键滚动加载所有内容
no_change_count = 0
last_count = 0
max_no_change = 1  # 连续1次数据条数不变，认为加载完成

while no_change_count < max_no_change:
    await page.keyboard.press("PageDown")
    await asyncio.sleep(2)
    
    # 检查数据条数变化
    current_products = await page.query_selector_all("div.van-list > div")
    current_count = len(current_products)
    
    if current_count == last_count:
        no_change_count += 1
    else:
        no_change_count = 0
        last_count = current_count
```

### 4.15 西藏省解析器 (xizang.py)

**页面特征**
- 列表容器：`div.list-box`
- 产品容器：`div.list-box > div`
- 支持PageDown键滚动加载

**解析逻辑**
```python
# 使用PageDown键滚动加载所有内容
no_change_count = 0
last_count = 0
max_no_change = 3  # 连续3次数据不变则认为加载完成

while no_change_count < max_no_change:
    await page.keyboard.press("PageDown")
    await asyncio.sleep(2)
    
    # 检查数据变化
    current_elements = await page.query_selector_all("div.list-box > div")
    current_count = len(current_elements)
    
    if current_count == last_count:
        no_change_count += 1
    else:
        no_change_count = 0
        last_count = current_count
```

**菜单配置**
- 等待选择器：`div.w-13.menu-content`
- 菜单选择器：`div.w-13.menu-content > div.top.clearfix > div`
- 列表选择器：`div.list-box > div`

### 4.16 浙江省解析器 (zhejiang.py)

**页面特征**
- 采用瀑布流加载方式
- 产品卡片：`div.newZifei > div.model`
- 需要暴力PageDown滚动

**解析逻辑**
```python
# 暴力PageDown滚动方案
max_retries = 200
retry_count = 0
last_count = 0
same_count_times = 0

while retry_count < max_retries:
    cards = await page.query_selector_all("div.newZifei > div.model")
    current_count = len(cards)
    
    if current_count == last_count:
        same_count_times += 1
        if same_count_times >= 5:  # 连续5次数量不变，认为加载完成
            break
    else:
        same_count_times = 0
        last_count = current_count
    
    await page.keyboard.press("PageDown")
    await page.wait_for_timeout(1000)
    retry_count += 1
```

**数据结构特点**
- 包含额外费用信息：`extra_fees`
- 其他内容说明：`other_content`
- 其他说明：`others`

---

## 5. 页面操作流程

### 5.1 页面访问步骤

| 步骤 | 操作内容 | 页面地址 | 等待时间 |
|------|----------|----------|----------|
| 1 | 访问省份资费页面 | `https://www.189.cn/jtzfzq/{省份代码}/` | 等待页面加载（最长30秒） |
| 2 | 检测页面类型 | 同上 | 检测iframe或API配置 |
| 3 | 进入iframe页面 | iframe src地址 | 等待iframe加载（最长10秒） |
| 4 | 处理菜单导航 | iframe内页面 | 等待菜单出现（最长10秒） |
| 5 | 遍历菜单项 | 同上 | 每次点击后等待2秒 |
| 6 | 滚动加载内容 | 同上 | 根据省份策略滚动 |

### 5.2 iframe页面处理流程

**iframe检测和访问**
```python
# 检测iframe
iframe = await self.page.query_selector("iframe")
if not iframe:
    raise Exception("未找到iframe")

# 获取iframe地址
iframe_src = await iframe.get_attribute('src')

# 创建新页面访问iframe
new_page = await self.context.new_page()
await new_page.goto(iframe_src)
```

**特殊iframe地址映射**
```python
iframe_urls = {
    'nx': 'https://yx.nx.189.cn/m_nx_main/newTariffZone/toIndex.do?source=H5',
    'he': 'https://waphe.189.cn/wap_heb/h5/productAnnounce/index.html',
    'ah': 'https://wapah.189.cn/tariff/h5toIndex.wap',
    'jx': 'https://wapjx.189.cn/wx/charges/index.html',
    'js': 'https://wapjs.189.cn/mall/pages/feePart/index.html',
    'gs': 'https://gs.189.cn/wt/tariffZone'
}
```

### 5.3 API拦截处理流程

**设置请求拦截器**
```python
async def intercept_response(route, request):
    url = request.url
    response = await route.fetch()
    
    # 获取当前省份的API URL
    target_url = self.API_URLS.get(self.province_code)
    if target_url and url.startswith(target_url):
        response_body = await response.text()
        await self._save_api_response(response_body, url)
    
    await route.fulfill(response=response)

# 设置拦截器
await new_page.route("**/*", intercept_response)
```

---

## 6. 数据结构说明

### 6.1 标准数据字段

| 字段名称 | 字段说明 | 数据示例 | 备注 |
|----------|----------|----------|------|
| title | 套餐名称 | "电信5G畅享套餐" | 核心标识字段 |
| code | 方案编号 | "TC2024001" | 去除空格后的编号 |
| basic_info | 基本信息 | 键值对对象 | 月租、流量、语音等 |
| service_content | 服务内容 | 键值对对象 | 详细服务说明 |
| remark | 备注信息 | 文本字符串 | 使用说明和注意事项 |
| others | 其他说明 | 文本字符串 | 补充信息（部分省份） |

### 6.2 浙江省特殊字段

| 字段名称 | 字段说明 | 数据示例 |
|----------|----------|----------|
| extra_fees | 额外费用 | "超出部分按标准资费收取" |
| other_content | 其他内容 | "包含来电显示等增值服务" |
| others | 其他说明 | "具体以当地营业厅为准" |

### 6.3 江苏省城市数据结构
```json
{
  "南京_5G套餐": [
    {
      "title": "电信5G畅享套餐",
      "code": "NJ2024001",
      "basic_info": {...},
      "service_content": {...},
      "remark": "..."
    }
  ],
  "苏州_5G套餐": [...]
}
```

### 6.4 API响应数据结构
API接口省份直接保存原始JSON响应：
```json
{
  "code": "200",
  "message": "success",
  "data": [
    {
      "packageName": "5G畅享套餐",
      "monthlyFee": "129",
      "dataAllowance": "30GB",
      "voiceMinutes": "500"
    }
  ]
}
```

---

## 7. 时间控制策略

### 7.1 等待时间设置

| 等待类型 | 时间设置 | 适用省份 | 业务原因 |
|----------|----------|----------|----------|
| 页面初始加载 | 30秒 | 所有省份 | 确保主页面完全加载 |
| iframe加载等待 | 10秒 | 定制化省份 | 等待iframe页面初始化 |
| 菜单点击等待 | 2秒 | 所有省份 | 等待页面响应用户操作 |
| 滚动加载等待 | 2秒 | 需要滚动的省份 | 等待动态内容加载 |
| 深度滚动等待 | 12-15秒 | 复杂页面省份 | 处理复杂页面加载 |
| API响应等待 | 5秒 | API接口省份 | 等待网络请求完成 |

### 7.2 省份特殊时间控制

**湖南省（van-list组件）**
- 滚动间隔：2秒
- 最大无变化次数：1次
- 适用于移动端组件的快速加载

**西藏省（复杂页面）**
- 滚动间隔：2秒
- 最大无变化次数：3次
- 适用于内容较多的页面

**浙江省（瀑布流）**
- 滚动间隔：1秒
- 最大重试次数：200次
- 连续无变化次数：5次
- 适用于瀑布流无限滚动页面

**江苏省（城市切换）**
- 城市切换等待：2-3秒
- 菜单处理等待：1-2秒
- 数据保存等待：标准流程

---

## 8. 质量保证机制

### 8.1 多重验证机制

**页面加载验证**
- iframe存在性检查
- 关键元素可见性验证
- 页面内容完整性检查

**数据完整性验证**
- 必要字段存在性检查
- 数据格式正确性验证
- 重复数据自动去除

**省份特殊验证**
- 江苏省：城市数据完整性验证
- 浙江省：瀑布流加载完成验证
- API省份：响应数据有效性验证

### 8.2 错误处理策略

**分层重试机制**
- 页面加载失败：重试3次，间隔递增
- 元素定位失败：重试3次，立即重试
- 数据提取异常：跳过当前项，继续处理
- API请求失败：记录错误，继续其他操作

**降级处理方案**
- 部分菜单项失败：保存已成功的数据
- iframe加载失败：尝试直接访问iframe地址
- 滚动加载不完整：保存已加载的内容
- 城市数据部分失败：保存成功城市的数据

### 8.3 数据验证规则

**通用验证规则**
- 套餐名称不能为空
- 至少包含基本信息或服务内容之一
- 方案编号格式合理（去除空格后）

**省份特殊验证**
- 江苏省：每个城市至少1条数据
- 浙江省：包含额外费用等特殊字段
- API省份：JSON格式有效性验证

---

## 9. 输出结果说明

### 9.1 文件输出格式

**定制化省份JSON文件**
- 文件名格式：`省份名_日期_随机数_省份名资费.json`
- 示例：`安徽_20241201_1234_安徽资费.json`
- 内容：该省份所有菜单类型的结构化数据

**江苏省城市文件**
- 文件名格式：`江苏_城市名_日期_随机数_江苏资费.json`
- 示例：`江苏_南京_20241201_1234_江苏资费.json`
- 内容：该城市的完整资费数据

**API省份响应文件**
- 文件名格式：`标签页名_日期时间_随机数_套餐.json`
- 示例：`全网资费_20241201_123456_套餐.json`
- 内容：原始API响应数据

**ZIP压缩包**
- 文件名格式：`省份代码_telecom_随机数_资费数据.zip`
- 示例：`ah_telecom_1234_资费数据.zip`
- 内容：该省份所有JSON文件的压缩包

### 9.2 数据文件结构示例

**定制化省份数据结构**
```json
{
  "安徽_5G套餐": [
    {
      "title": "电信5G畅享套餐129元",
      "code": "AH2024001",
      "basic_info": {
        "月租": "129元",
        "国内流量": "30GB",
        "国内语音": "500分钟"
      },
      "service_content": {
        "增值服务": "来电显示、189邮箱",
        "特色服务": "5G网络优享"
      },
      "remark": "超出部分按标准资费收取，具体以营业厅为准"
    }
  ],
  "安徽_4G套餐": [...]
}
```

**江苏省城市数据结构**
```json
{
  "南京_5G套餐": [
    {
      "title": "电信5G畅享套餐",
      "code": "NJ2024001",
      "basic_info": {...},
      "service_content": {...},
      "remark": "南京地区专享优惠"
    }
  ]
}
```

### 9.3 质量统计报告

**数据统计信息**
- 总采集套餐数量
- 各菜单类型分布
- 数据完整性评分
- 采集成功率统计

**省份特殊统计**
- 江苏省：各城市数据量统计
- 浙江省：瀑布流加载统计
- API省份：接口响应成功率

---

## 10. 业务应用场景

### 10.1 市场分析应用

**全国资费对比分析**
- 31个省份的完整资费数据对比
- 不同地区的定价策略分析
- 特色套餐和本地化服务识别

**竞品策略研究**
- 电信在不同省份的差异化策略
- 本地化产品设计思路分析
- 价格敏感度区域分析

### 10.2 数据价值特色

**高度定制化数据**
- 15个省份的深度定制化解析
- 保留各省份的特色数据结构
- 完整的本地化信息采集

**多源数据整合**
- HTML页面解析数据
- API接口原始数据
- 城市级别细分数据

**实时准确性**
- 直接从官方页面获取
- 支持iframe嵌套页面处理
- API数据实时拦截保存

---

## 11. 技术特色与创新

### 11.1 省份解析器架构

**模块化设计**
- 每个省份独立的解析模块
- 统一的接口规范
- 灵活的配置管理

**智能路由机制**
- 自动识别省份类型
- 动态选择处理策略
- 支持新省份快速接入

### 11.2 复杂页面处理能力

**iframe嵌套处理**
- 自动检测iframe结构
- 跨页面数据传递
- 独立的页面生命周期管理

**动态内容加载**
- 多种滚动策略支持
- 智能加载完成检测
- 内容变化实时监控

**API数据拦截**
- 网络请求实时拦截
- 响应数据自动保存
- 多接口并行处理

### 11.3 江苏省城市遍历创新

**城市级别数据采集**
- 自动识别城市列表
- 逐城市数据采集
- 城市数据独立存储

**数据关联管理**
- 城市与套餐数据关联
- 地域化信息保留
- 数据完整性保证

---

## 12. 维护和优化指南

### 12.1 省份解析器维护

**定期检查项目**
- 各省份页面结构变化监控
- 选择器有效性验证
- 数据字段完整性检查

**解析器更新流程**
1. 检测页面结构变化
2. 更新选择器配置
3. 调整解析逻辑
4. 验证数据输出
5. 更新文档说明

### 12.2 新省份接入指南

**定制化省份接入**
1. 创建省份解析器文件
2. 实现解析函数和配置函数
3. 添加到PROVINCE_PARSERS映射
4. 测试验证数据输出

**API省份接入**
1. 识别API接口地址
2. 添加到API_URLS配置
3. 测试API拦截功能
4. 验证数据保存

### 12.3 故障排查指南

**常见问题诊断**
1. **iframe加载失败**
   - 检查iframe地址是否变化
   - 验证页面加载超时设置
   - 确认网络连接状态

2. **选择器失效**
   - 检查页面DOM结构变化
   - 更新选择器配置
   - 测试新选择器有效性

3. **数据提取异常**
   - 检查数据字段结构变化
   - 验证解析逻辑正确性
   - 确认数据格式要求

4. **API拦截失败**
   - 检查API地址是否变化
   - 验证拦截器设置
   - 确认请求触发条件

### 12.4 性能优化建议

**时间控制优化**
- 根据省份特点调整等待时间
- 优化滚动加载策略
- 减少不必要的重试

**资源使用优化**
- 合理控制并发页面数量
- 及时释放页面资源
- 优化内存使用

**数据处理优化**
- 流式数据处理
- 增量数据更新
- 压缩文件优化

---

## 13. 注意事项

### 13.1 数据使用建议
- 数据仅供内部分析使用，请勿用于商业用途
- 定期更新数据，保持信息的时效性
- 注意各省份数据结构的差异性
- 使用江苏省数据时注意城市级别的区分

### 13.2 系统限制
- 定制化省份的维护成本较高
- 页面结构变化可能影响多个省份
- iframe页面的稳定性依赖于目标网站
- API接口的变化可能影响数据采集

### 13.3 合规要求
- 遵守相关法律法规和网站使用条款
- 合理控制访问频率，避免对服务器造成压力
- 数据使用应符合隐私保护要求
- 建议在使用前了解各省份网站的使用规则

---

*文档版本：v1.0*  
*更新时间：2024年12月*  
*适用对象：业务人员、产品经理、数据分析师、技术维护人员*