# 中国移动资费信息爬虫业务说明文档

## 目录
- [1. 系统概述](#1-系统概述)
- [2. 业务流程](#2-业务流程)
- [3. 数据采集逻辑](#3-数据采集逻辑)
- [4. 页面操作流程](#4-页面操作流程)
- [5. 数据结构说明](#5-数据结构说明)
- [6. 时间控制策略](#6-时间控制策略)
- [7. 质量保证机制](#7-质量保证机制)
- [8. 输出结果说明](#8-输出结果说明)

---

## 1. 系统概述

### 1.1 功能介绍
中国移动资费信息爬虫是一个自动化数据采集系统，能够：
- 自动访问中国移动官方资费页面
- 按省份采集全国各地的资费信息
- 自动分类整理不同类型的套餐数据
- 生成标准化的Excel数据报表

### 1.2 系统架构
```
用户请求 → API接口 → 爬虫引擎 → 中国移动官网 → 数据处理 → Excel文件 → 结果返回
```

### 1.3 核心价值
- **数据完整性**：覆盖全国31个省份的资费信息
- **数据准确性**：直接从官方网站获取最新数据
- **数据标准化**：统一的数据格式便于分析使用
- **自动化程度高**：无需人工干预，自动完成数据采集

---

## 2. 业务流程

### 2.1 整体业务流程图
```
开始任务
    ↓
选择目标省份
    ↓
访问省份资费页面
    ↓
识别资费分类导航
    ↓
遍历所有资费类型
    ↓
采集每种类型的详细数据
    ↓
数据清洗和标准化
    ↓
生成Excel报表
    ↓
打包压缩文件
    ↓
任务完成通知
```

### 2.2 省份处理流程
1. **省份选择**：支持单个省份或全国批量处理
2. **页面访问**：自动打开对应省份的资费页面
3. **数据采集**：按分类逐一采集资费信息
4. **结果输出**：生成该省份的完整数据文件

---

## 3. 数据采集逻辑

### 3.1 页面结构分析
中国移动资费页面采用三级分类结构：

**第一级：资费大类**（可选）
- 个人套餐
- 家庭套餐
- 企业套餐

**第二级：资费来源**（必选）
- 线上渠道
- 线下渠道
- 全部渠道

**第三级：资费类型**（必选）
- 5G套餐
- 4G套餐
- 流量包
- 语音包
- 国际漫游

### 3.2 采集策略
```
for 每个资费来源:
    for 每个资费类型:
        1. 点击选择分类
        2. 等待页面加载
        3. 滚动加载全部内容
        4. 提取所有资费信息
        5. 保存到数据集合
```

### 3.3 数据提取方法
- **瀑布流数据**：提取套餐名称、价格、流量等基础信息
- **表格数据**：提取详细的资费标准和使用规则
- **描述信息**：提取套餐说明和特色服务

---

## 4. 页面操作流程

### 4.1 页面访问步骤

| 步骤 | 操作内容 | 页面地址 | 等待时间 |
|------|----------|----------|----------|
| 1 | 打开省份资费页面 | `https://h.app.coc.10086.cn/cmcc-app/pc-pages/tariffZonePers.html?prov=省份代码` | 等待页面完全加载（最长60秒） |
| 2 | 定位导航菜单 | 同上 | 等待导航元素出现（最长60秒） |
| 3 | 选择资费来源 | 同上 | 点击后等待2秒 |
| 4 | 选择资费类型 | 同上 | 点击后等待2秒 |
| 5 | 滚动加载内容 | 同上 | 持续滚动直到加载完成（最长30秒） |

### 4.2 页面交互说明

**导航点击操作**
- 每次点击导航选项后，系统会等待2秒让页面响应
- 如果页面加载失败，会自动重试最多3次
- 重试间隔逐渐增加：第1次立即重试，第2次等待5秒，第3次等待10秒

**滚动加载机制**
- 自动向下滚动页面，触发动态内容加载
- 每次滚动后等待2秒，让新内容完全显示
- 通过检测页面高度和内容数量变化，判断是否加载完成
- 如果连续3次滚动都没有新内容，则认为加载完成

---

## 5. 数据结构说明

### 5.1 采集的数据字段

| 字段名称 | 字段说明 | 数据示例 | 备注 |
|----------|----------|----------|------|
| 主套餐 | 套餐名称 | "移动王卡19元套餐" | 核心标识字段 |
| 类别 | 套餐分类 | "4G套餐" | 用于分类统计 |
| 资费来源 | 销售渠道 | "线上" | 区分获取渠道 |
| 资费类型 | 具体类型 | "流量套餐" | 细分类别 |
| 资费大类 | 大分类 | "个人套餐" | 顶级分类 |
| 月租 | 月费价格 | "19元/月" | 基础费用 |
| 通用流量 | 流量额度 | "30GB" | 流量配额 |
| 语音 | 通话时长 | "100分钟" | 语音配额 |
| 短彩信 | 短信数量 | "100条" | 短信配额 |

### 5.2 数据质量标准
- **完整性**：每条记录必须包含主套餐、类别、资费来源、资费类型等核心字段
- **准确性**：数据直接从官网提取，保证信息的真实性和时效性
- **一致性**：相同类型的数据采用统一的格式和单位
- **可读性**：数据格式便于人工阅读和系统处理

---

## 6. 时间控制策略

### 6.1 等待时间设置

| 等待类型 | 时间设置 | 业务原因 | 调整建议 |
|----------|----------|----------|----------|
| 页面初始加载 | 60秒 | 确保页面完全打开 | 网络慢时可适当延长 |
| 导航点击等待 | 2秒 | 让页面响应用户操作 | 一般不需要调整 |
| 滚动加载等待 | 2秒 | 等待动态内容显示 | 可根据网速调整 |
| 深度滚动等待 | 10秒 | 处理复杂页面加载 | 内容多时可延长 |
| 最大滚动时间 | 30秒 | 避免无限等待 | 可根据页面复杂度调整 |

### 6.2 时间控制的业务意义
- **保证数据完整性**：充分的等待时间确保所有内容都能加载出来
- **提高成功率**：合理的重试和等待机制减少因网络波动导致的失败
- **平衡效率与质量**：在数据质量和采集速度之间找到最佳平衡点

---

## 7. 质量保证机制

### 7.1 多重验证机制
1. **页面加载验证**：确认关键页面元素已正确加载
2. **数据完整性验证**：检查采集到的数据是否包含必要字段
3. **数据格式验证**：确保数据格式符合预期标准
4. **重复数据检测**：自动去除重复的资费信息

### 7.2 错误处理策略
- **自动重试**：遇到临时性错误时自动重试，最多3次
- **错误记录**：详细记录所有错误信息，便于问题分析
- **降级处理**：部分数据采集失败时，保存已成功采集的数据
- **异常恢复**：系统异常时自动恢复到稳定状态

### 7.3 数据验证规则
- 套餐名称不能为空
- 价格信息必须包含数字
- 流量、语音等配额必须有明确单位
- 每个省份至少要有50条以上的有效数据

---

## 8. 输出结果说明

### 8.1 文件输出格式

**Excel文件**
- 文件名格式：`省份名_mobile_随机数_资费来源_资费类型.xlsx`
- 示例：`北京_mobile_123456_线上_5G套餐.xlsx`
- 内容：该省份该类型的所有资费信息

**ZIP压缩包**
- 包含该省份所有类型的Excel文件
- 文件名格式：`省份名_mobile_日期时间.zip`
- 便于批量下载和存储

### 8.2 数据统计信息

每次采集完成后，系统会提供以下统计信息：
- 总采集条数
- 各类型套餐数量分布
- 价格区间分析
- 采集成功率

### 8.3 质量报告

**数据质量指标**
- 完整性：有效数据占比
- 准确性：数据格式正确率
- 时效性：数据采集时间
- 覆盖度：分类覆盖完整性

**异常情况说明**
- 如果某个分类下没有数据，会在报告中特别说明
- 如果采集过程中遇到页面变化，会记录具体情况
- 如果数据量异常（过多或过少），会进行标注提醒

---

## 9. 业务应用场景

### 9.1 市场分析
- **竞品分析**：了解移动各省份的资费策略
- **价格监控**：跟踪资费变化趋势
- **产品对比**：分析不同套餐的性价比

### 9.2 运营决策
- **定价策略**：参考移动的定价模式
- **产品设计**：了解市场上的主流套餐配置
- **渠道策略**：分析线上线下渠道的产品差异

### 9.3 数据价值
- **实时性**：获取最新的官方资费信息
- **全面性**：覆盖全国各省份的完整数据
- **标准化**：统一格式便于后续分析处理
- **可追溯**：保留采集时间和来源信息

---

## 10. 注意事项

### 10.1 数据使用建议
- 数据仅供内部分析使用，请勿用于商业用途
- 建议定期更新数据，保持信息的时效性
- 使用数据时请注意保护用户隐私和商业机密

### 10.2 系统限制
- 采集速度受网络状况影响
- 目标网站结构变化可能影响数据采集
- 大批量采集时需要考虑对目标服务器的影响

### 10.3 合规要求
- 遵守相关法律法规和网站使用条款
- 合理控制访问频率，避免对服务器造成压力
- 数据使用应符合相关的隐私保护要求

---

*文档版本：v1.0*  
*更新时间：2024年12月*  
*适用对象：业务人员、产品经理、数据分析师*