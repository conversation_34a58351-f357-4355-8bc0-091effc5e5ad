# 中国广电资费信息爬虫业务说明文档

## 目录
- [1. 系统概述](#1-系统概述)
- [2. 业务流程](#2-业务流程)
- [3. 数据采集逻辑](#3-数据采集逻辑)
- [4. 页面操作流程](#4-页面操作流程)
- [5. 数据结构说明](#5-数据结构说明)
- [6. 时间控制策略](#6-时间控制策略)
- [7. 质量保证机制](#7-质量保证机制)
- [8. 输出结果说明](#8-输出结果说明)

---

## 1. 系统概述

### 1.1 功能介绍
中国广电资费信息爬虫是一个专门针对中国广电网络的自动化数据采集系统，能够：
- 自动访问中国广电官方资费公示页面
- 按省份采集全国各地的广电资费信息
- 自动处理多级下拉菜单和标签页切换
- 生成标准化的JSON数据文件和ZIP压缩包

### 1.2 系统架构
```
用户请求 → API接口 → 爬虫引擎 → 中国广电官网 → 数据处理 → JSON文件 → ZIP打包 → 回调通知
```

### 1.3 核心价值
- **全省份覆盖**：支持全国各省份的广电资费信息采集
- **数据准确性**：直接从官方资费公示页面获取最新数据
- **结构化数据**：自动解析复杂的表格结构，生成标准化数据
- **智能处理**：自动处理跨行单元格、多标签页等复杂页面结构

---

## 2. 业务流程

### 2.1 整体业务流程图
```
开始任务
    ↓
访问广电资费公示首页
    ↓
点击省份选择下拉框
    ↓
遍历所有省份选项
    ↓
选择目标省份
    ↓
处理资费分类标签页
    ↓
处理二级下拉菜单
    ↓
提取套餐详细信息
    ↓
数据清洗和结构化
    ↓
保存JSON文件
    ↓
创建ZIP压缩包
    ↓
发送回调通知
    ↓
处理下一个省份
```

### 2.2 省份处理流程
1. **省份选择**：从下拉菜单中选择目标省份
2. **页面切换**：等待页面内容更新为选中省份的数据
3. **标签页处理**：依次处理"全网资费"和"本省资费"两个标签页
4. **数据采集**：在每个标签页下遍历所有下拉选项
5. **结果输出**：生成该省份的完整数据文件

---

## 3. 数据采集逻辑

### 3.1 页面结构分析
中国广电资费页面采用多层级结构：

**第一级：省份选择**（必选）
- 通过下拉菜单选择具体省份
- 页面内容会根据省份动态更新

**第二级：资费分类标签页**（必选）
- 全网资费：适用于全国的统一资费
- 本省资费：该省份特有的本地资费

**第三级：业务类型下拉菜单**（必选）
- 套餐类：各种套餐产品
- 基础业务类：基础通信服务
- 增值业务类：附加增值服务

### 3.2 采集策略
```
for 每个目标省份:
    选择省份()
    for 每个资费标签页 in ["全网资费", "本省资费"]:
        点击标签页()
        for 每个下拉选项:
            选择下拉选项()
            等待页面加载()
            提取套餐数据()
            保存到数据集合()
```

### 3.3 数据提取方法
- **标签页数据**：处理"基础业务"和"其它业务"两个子标签页
- **表格数据**：智能处理跨行单元格（rowspan）和复杂表头
- **套餐信息**：提取套餐名称、详细规格、价格等核心信息
- **详情信息**：提取套餐说明、使用条件等补充信息

---

## 4. 页面操作流程

### 4.1 页面访问步骤

| 步骤 | 操作内容 | 页面地址 | 等待时间 |
|------|----------|----------|----------|
| 1 | 打开广电资费公示首页 | `https://m.10099.com.cn/expensesNotice/#/home` | 等待页面完全加载（最长30秒） |
| 2 | 点击省份下拉箭头 | 同上 | 等待下拉菜单展开（最长10秒） |
| 3 | 选择目标省份 | 同上 | 点击后等待3-5秒 |
| 4 | 点击资费分类标签页 | 同上 | 点击后等待2-3秒 |
| 5 | 选择业务类型下拉选项 | 同上 | 每次选择后等待2-3秒 |
| 6 | 提取页面数据 | 同上 | 数据提取完成后等待1-2秒 |

### 4.2 页面交互说明

**省份选择机制**
- 首先定位省份选择区域的下拉箭头图标
- 点击后等待省份选项列表完全展开
- 遍历所有可用省份选项，支持指定省份或全省份采集
- 每次选择省份后等待页面内容更新

**标签页切换处理**
- 自动识别"全网资费"和"本省资费"两个主标签页
- 在每个主标签页下处理二级下拉菜单
- 智能处理页面加载状态，确保数据完整性

**下拉菜单操作**
- 自动定位并点击下拉菜单触发元素
- 等待下拉选项完全加载后再进行选择
- 支持重试机制，处理页面加载异常情况

---

## 5. 数据结构说明

### 5.1 采集的数据字段

| 字段名称 | 字段说明 | 数据示例 | 备注 |
|----------|----------|----------|------|
| 套餐名称 | 资费套餐的名称 | "广电5G畅享套餐" | 核心标识字段 |
| 基础业务 | 基础通信服务详情 | 表格数据数组 | 包含流量、语音等 |
| 其它业务 | 增值服务详情 | 表格数据数组 | 包含附加服务 |
| 详细信息 | 套餐补充说明 | 文本描述 | 使用条件、注意事项等 |
| 省份信息 | 所属省份 | "北京" | 数据来源标识 |
| 资费类型 | 全网或本省资费 | "全网资费" | 分类标识 |
| 业务分类 | 具体业务类型 | "套餐类" | 细分类别 |

### 5.2 表格数据结构
基础业务和其它业务采用表格形式存储：
```json
{
  "基础业务": [
    {
      "项目名称": "国内流量",
      "包含内容": "30GB",
      "超出资费": "5元/GB",
      "说明": "当月有效"
    }
  ],
  "其它业务": [
    {
      "业务名称": "来电显示",
      "月功能费": "6元",
      "开通方式": "默认开通"
    }
  ]
}
```

### 5.3 数据质量标准
- **完整性**：每条记录必须包含套餐名称和至少一种业务类型的详细信息
- **准确性**：数据直接从官网表格提取，保证信息的真实性
- **结构化**：复杂表格数据转换为标准JSON格式
- **关联性**：保持跨行单元格的数据关联关系

---

## 6. 时间控制策略

### 6.1 等待时间设置

| 等待类型 | 时间设置 | 业务原因 | 调整建议 |
|----------|----------|----------|----------|
| 页面初始加载 | 30秒 | 确保SPA应用完全初始化 | 网络慢时可延长至60秒 |
| 省份选择等待 | 3-5秒 | 等待页面内容动态更新 | 根据网速可调整为2-8秒 |
| 标签页切换等待 | 2-3秒 | 等待标签页内容加载 | 一般不需要调整 |
| 下拉菜单等待 | 2-3秒 | 等待下拉选项和页面响应 | 复杂页面可延长至5秒 |
| 数据提取等待 | 1-2秒 | 确保DOM元素稳定 | 通常不需要调整 |
| 重试等待间隔 | 2秒 | 避免频繁请求 | 可根据服务器响应调整 |

### 6.2 智能等待机制
- **动态页面检测**：检测页面加载状态，避免固定等待时间的浪费
- **元素可见性验证**：确保目标元素完全加载后再进行操作
- **内容变化监控**：监控页面内容变化，确保数据更新完成
- **异常恢复等待**：遇到异常时采用递增等待时间

### 6.3 时间控制的业务意义
- **保证数据完整性**：充分的等待时间确保动态内容完全加载
- **提高采集成功率**：合理的等待和重试机制减少因页面加载问题导致的失败
- **平衡效率与稳定性**：在采集速度和系统稳定性之间找到最佳平衡

---

## 7. 质量保证机制

### 7.1 多重验证机制
1. **页面状态验证**：确认页面完全加载且处于可操作状态
2. **元素存在性验证**：验证关键页面元素是否正确加载
3. **数据完整性验证**：检查提取的数据是否包含必要字段
4. **表格结构验证**：验证表格数据的行列对应关系

### 7.2 错误处理策略
- **多层重试机制**：
  - 元素定位失败：重试3次
  - 页面加载超时：重试3次
  - 数据提取异常：切换到其他选项后重试
- **降级处理**：部分数据采集失败时，保存已成功采集的数据
- **异常记录**：详细记录所有异常情况，便于问题分析和优化
- **页面备份**：保存关键页面的HTML源码，用于调试和分析

### 7.3 数据验证规则
- 套餐名称不能为空且长度合理
- 基础业务或其它业务至少有一项包含数据
- 表格数据必须有明确的字段名和对应值
- 每个省份每个分类至少要有1条以上的有效数据

### 7.4 智能处理机制
- **跨行单元格处理**：自动识别和处理HTML表格中的rowspan属性
- **动态表头解析**：智能解析多行表头，合并表头文本
- **空数据过滤**：自动过滤空白和无效的数据行
- **重复数据去除**：避免因页面重复加载导致的数据重复

---

## 8. 输出结果说明

### 8.1 文件输出格式

**JSON文件**
- 文件名格式：`省份名_broadnet_日期_随机数_标签页名称.json`
- 示例：`北京_broadnet_20241201_1234_全网资费.json`
- 内容：该省份该标签页下的所有资费信息，采用JSON格式存储

**ZIP压缩包**
- 包含该省份所有标签页的JSON文件
- 文件名格式：`省份名_broadnet_随机数_资费数据.zip`
- 便于批量下载和数据传输

### 8.2 数据文件结构
```json
{
  "全网资费": {
    "套餐类": [
      {
        "套餐名称": "广电5G畅享套餐",
        "基础业务": [...],
        "其它业务": [...],
        "详细信息": {...}
      }
    ],
    "基础业务类": [...],
    "增值业务类": [...]
  }
}
```

### 8.3 质量报告

**数据统计信息**
- 总采集套餐数量
- 各业务类型分布统计
- 数据完整性评分
- 采集成功率统计

**异常情况记录**
- 页面加载异常的省份和页面
- 数据提取失败的具体原因
- 重试次数和最终结果
- 建议的优化措施

### 8.4 回调通知机制
- **成功回调**：数据采集完成后自动发送ZIP文件到指定回调URL
- **失败回调**：采集失败时发送错误状态通知
- **进度通知**：支持实时进度更新（可选）
- **状态码说明**：200表示成功，500表示失败

---

## 9. 业务应用场景

### 9.1 市场分析
- **广电资费监控**：跟踪广电网络在各省份的资费策略
- **竞品对比分析**：与其他运营商资费进行横向比较
- **价格趋势分析**：监控资费变化趋势和调整规律
- **区域差异分析**：分析不同省份的资费差异和特色

### 9.2 运营决策支持
- **定价策略参考**：了解广电的定价模式和策略
- **产品设计借鉴**：分析广电的套餐设计思路
- **市场定位分析**：理解广电在不同市场的定位策略
- **服务内容对比**：比较基础业务和增值业务的配置

### 9.3 数据价值
- **官方数据源**：直接来源于官方资费公示页面，权威可靠
- **实时更新**：支持定期采集，获取最新的资费信息
- **结构化处理**：复杂表格数据转换为易于分析的JSON格式
- **全面覆盖**：支持全国各省份的完整数据采集

---

## 10. 技术特色

### 10.1 智能表格解析
- **跨行单元格处理**：自动识别和处理HTML表格中的rowspan属性
- **动态表头识别**：智能解析多行表头结构
- **数据关联维护**：保持表格数据的逻辑关联关系
- **格式标准化**：将复杂表格转换为标准JSON结构

### 10.2 页面交互优化
- **SPA应用适配**：专门优化单页面应用的交互处理
- **动态内容等待**：智能等待动态内容加载完成
- **多层级菜单处理**：自动处理复杂的下拉菜单结构
- **状态检测机制**：实时检测页面和元素状态

### 10.3 异常处理能力
- **多重重试策略**：针对不同类型异常采用不同重试策略
- **智能恢复机制**：自动从异常状态恢复到正常流程
- **详细错误记录**：完整记录异常信息，便于问题定位
- **降级处理方案**：确保部分数据丢失时不影响整体任务

---

## 11. 注意事项

### 11.1 数据使用建议
- 数据仅供内部分析使用，请勿用于商业用途
- 建议定期更新数据，保持信息的时效性
- 使用数据时请注意保护商业机密和用户隐私
- 数据分析时需要考虑地域差异和政策因素

### 11.2 系统限制
- 采集速度受目标网站响应速度影响
- 页面结构变化可能影响数据采集效果
- 大批量采集时需要合理控制并发数量
- 部分省份可能存在数据更新延迟

### 11.3 合规要求
- 严格遵守相关法律法规和网站使用条款
- 合理控制访问频率，避免对服务器造成压力
- 数据使用应符合相关的隐私保护和商业道德要求
- 建议在使用前了解目标网站的robots.txt规则

---

## 12. 维护和优化

### 12.1 定期维护建议
- **页面结构检查**：定期检查目标页面结构是否发生变化
- **选择器更新**：根据页面变化及时更新元素选择器
- **性能优化**：根据采集效果调整等待时间和重试策略
- **数据质量监控**：定期检查数据质量和完整性

### 12.2 故障排查指南
1. **检查网络连接**：确保能够正常访问目标网站
2. **验证页面结构**：检查页面元素是否发生变化
3. **查看错误日志**：分析详细的错误信息和堆栈跟踪
4. **测试单个省份**：先测试单个省份确认基本功能
5. **检查回调接口**：确认回调URL可以正常接收数据

### 12.3 性能优化建议
- 根据网络状况调整等待时间参数
- 优化重试策略，减少不必要的重试
- 合理设置并发数量，平衡速度和稳定性
- 定期清理临时文件和日志，释放存储空间

---

*文档版本：v1.0*  
*更新时间：2024年12月*  
*适用对象：业务人员、产品经理、数据分析师*