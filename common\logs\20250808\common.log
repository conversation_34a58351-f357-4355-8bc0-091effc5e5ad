2025-08-08 13:48:50 - MainProcess - 6948 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250808
2025-08-08 13:48:52 - SpawnProcess-1 - 33368 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250808
2025-08-08 13:50:37 - MainProcess - 3900 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250808
2025-08-08 13:50:38 - SpawnProcess-1 - 7932 - common - INFO - [logger_config.py:78] - 日志文件保存在: D:\PythonProjects\xty\common\logs\20250808
2025-08-08 13:50:43 - SpawnProcess-1 - 7932 - common - DEBUG - [window_utils.py:21] - 屏幕尺寸: 1920 x 1080
2025-08-08 13:50:43 - SpawnProcess-1 - 7932 - common - DEBUG - [window_utils.py:41] - 自适应窗口尺寸: 1536 x 864
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - DEBUG - [window_utils.py:60] - 屏幕尺寸: 1920 x 1080
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - DEBUG - [window_utils.py:70] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - DEBUG - [window_utils.py:75] - 原始窗口尺寸: 1239 x 647
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - DEBUG - [window_utils.py:76] - 原始窗口位置: (192, 192)
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - DEBUG - [window_utils.py:85] - 目标尺寸: 576 x 1040
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - DEBUG - [window_utils.py:86] - 目标位置: (1344, 0)
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - INFO - [window_utils.py:94] - 窗口 'Anaconda Prompt - python  main.py' 已成功调整为右半屏分屏模式
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - DEBUG - [window_utils.py:223] - 找到目标窗口: 'Anaconda Prompt - python  main.py'
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - ERROR - [window_utils.py:243] - 激活窗口时出错: 'Win32Window' object has no attribute 'raise_'
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - INFO - [screen_record.py:40] - 录屏采集方式: dshow
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - INFO - [screen_record.py:44] - 请确保已安装 screen-capture-recorder。下载地址: https://github.com/rdp/screen-capture-recorder-to-video-windows-free/releases
2025-08-08 13:50:46 - SpawnProcess-1 - 7932 - common - INFO - [screen_record.py:82] - 开始录屏...
2025-08-08 13:50:47 - SpawnProcess-1 - 7932 - common - INFO - [screen_record.py:59] - [ffmpeg] [libx264 @ 0000028a67fa80c0] 264 - core 164 r3204 373697b - H.264/MPEG-4 AVC codec - Copyleft 2003-2025 - http://www.videolan.org/x264.html - options: cabac=1 ref=1 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=2 psy=1 psy_rd=1.00:0.00 mixed_ref=0 me_range=16 chroma_me=1 trellis=0 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=0 threads=24 lookahead_threads=8 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=1 keyint=250 keyint_min=10 scenecut=40 intra_refresh=0 rc_lookahead=10 rc=crf mbtree=1 crf=28.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
